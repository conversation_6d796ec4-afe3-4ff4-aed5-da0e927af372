/**
 * Global respondent details options for survey components
 * Used in both create survey and edit survey components
 */

/**
 * Interface for option items used in select, radio, and checkbox inputs
 */
export interface SelectOption {
  value: string;
  label: string;
}

/**
 * Enhanced interface for respondent detail options with flexible configuration
 */
export interface RespondentDetailOption {
  value: string; // internal name (e.g., "email")
  label: string; // label shown to user
  inputType: string; // e.g., "text", "select", "email", "radio", "checkbox", "number"
  required?: boolean; // whether the field is required
  placeholder?: string; // optional placeholder text
  options?: SelectOption[]; // only for select/radio/checkbox fields
  defaultValue?: any; // prefilled value if any
}

/**
 * List of accepted input types for respondent details
 */
export const AcceptedInputTypes = [
  { value: 'text', label: 'Text' },
  { value: 'email', label: 'Email' },
  { value: 'select', label: 'Dropdown' },
  { value: 'radio', label: 'Single Choice' },
  { value: 'checkbox', label: 'Multi Choice' },
  { value: 'number', label: 'Number' },
];

/**
 * Legacy respondent details options - kept for backward compatibility
 */
export const RespondentDetailsOptions: RespondentDetailOption[] = [
  { value: '-', label: 'Choose Respondent Details', inputType: 'text' },
  { value: 'fullName', label: 'Full Name', inputType: 'text', placeholder: 'Enter your full name' },
  { value: 'email', label: 'Email', inputType: 'email', placeholder: 'Enter your email address' },
  {
    value: 'age',
    label: 'Age',
    inputType: 'select',
    placeholder: 'Select your age range',
    options: [
      { value: 'under_18', label: 'Under 18' },
      { value: '18_24', label: '18-24' },
      { value: '25_34', label: '25-34' },
      { value: '35_44', label: '35-44' },
      { value: '45_54', label: '45-54' },
      { value: '55_64', label: '55-64' },
      { value: '65_plus', label: '65+' },
    ],
  },
  {
    value: 'gender',
    label: 'Gender',
    inputType: 'select',
    placeholder: 'Select your gender',
    options: [
      { value: 'male', label: 'Male' },
      { value: 'female', label: 'Female' },
      { value: 'non_binary', label: 'Non-binary' },
      { value: 'prefer_not_to_say', label: 'Prefer not to say' },
    ],
  },
  {
    value: 'jobTitle',
    label: 'Job Title / Role',
    inputType: 'text',
    placeholder: 'Enter your job title',
  },
  {
    value: 'seniority',
    label: 'Seniority Level',
    inputType: 'select',
    placeholder: 'Select your seniority level',
    options: [
      { value: 'entry', label: 'Entry Level' },
      { value: 'mid', label: 'Mid Level' },
      { value: 'senior', label: 'Senior Level' },
      { value: 'manager', label: 'Manager' },
      { value: 'director', label: 'Director' },
      { value: 'executive', label: 'Executive' },
    ],
  },
  {
    value: 'department',
    label: 'Department',
    inputType: 'select',
    placeholder: 'Select your department',
    options: [
      { value: 'engineering', label: 'Engineering' },
      { value: 'product', label: 'Product' },
      { value: 'design', label: 'Design' },
      { value: 'marketing', label: 'Marketing' },
      { value: 'sales', label: 'Sales' },
      { value: 'customer_support', label: 'Customer Support' },
      { value: 'hr', label: 'Human Resources' },
      { value: 'finance', label: 'Finance' },
      { value: 'operations', label: 'Operations' },
      { value: 'other', label: 'Other' },
    ],
  },
  {
    value: 'employmentType',
    label: 'Employment Type',
    inputType: 'select',
    placeholder: 'Select your employment type',
    options: [
      { value: 'full_time', label: 'Full-time' },
      { value: 'part_time', label: 'Part-time' },
      { value: 'contract', label: 'Contract' },
      { value: 'freelance', label: 'Freelance' },
      { value: 'intern', label: 'Intern' },
    ],
  },
  {
    value: 'organisationName',
    label: 'Organisation Name',
    inputType: 'text',
    placeholder: 'Enter your organization name',
  },
  {
    value: 'organisationSize',
    label: 'Organisation Size',
    inputType: 'select',
    placeholder: 'Select your organization size',
    options: [
      { value: '1_10', label: '1-10 employees' },
      { value: '11_50', label: '11-50 employees' },
      { value: '51_200', label: '51-200 employees' },
      { value: '201_500', label: '201-500 employees' },
      { value: '501_1000', label: '501-1000 employees' },
      { value: '1001_plus', label: '1001+ employees' },
    ],
  },
  {
    value: 'industry',
    label: 'Industry',
    inputType: 'select',
    placeholder: 'Select your industry',
    options: [
      { value: 'technology', label: 'Technology' },
      { value: 'healthcare', label: 'Healthcare' },
      { value: 'finance', label: 'Finance' },
      { value: 'education', label: 'Education' },
      { value: 'retail', label: 'Retail' },
      { value: 'manufacturing', label: 'Manufacturing' },
      { value: 'services', label: 'Services' },
      { value: 'other', label: 'Other' },
    ],
  },
  { value: 'custom', label: '+ Create Custom Detail', inputType: 'text' },
];
